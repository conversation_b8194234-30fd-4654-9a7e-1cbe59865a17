#!/usr/bin/env python3
"""
Setup script for RecLM environments
Creates separate conda environments for base models and LLM fine-tuning
"""

import subprocess
import sys
import os

def run_command(cmd, check=True):
    """Run a shell command and return the result"""
    print(f"Running: {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, check=check, 
                              capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        return None

def check_conda():
    """Check if conda is available"""
    result = run_command("which conda", check=False)
    return result and result.returncode == 0

def create_base_environment():
    """Create environment for base recommendation models"""
    print("\n=== Creating Base Models Environment ===")
    
    # Remove existing environment if it exists
    run_command("conda env remove -n reclm_base -y", check=False)
    
    # Create new environment
    if os.path.exists("environment_base.yml"):
        result = run_command("conda env create -f environment_base.yml")
        if result and result.returncode == 0:
            print("✓ Base environment created successfully")
            return True
    else:
        print("environment_base.yml not found, creating manually...")
        commands = [
            "conda create -n reclm_base python=3.9.13 -y",
            "conda activate reclm_base && conda install numpy=1.23.1 scipy=1.9.1 -y",
            "conda activate reclm_base && conda install pytorch=1.11.0 torchvision torchaudio cpuonly -c pytorch -y",
            "conda activate reclm_base && conda install matplotlib pandas scikit-learn tqdm -y"
        ]
        
        for cmd in commands:
            result = run_command(cmd)
            if not result or result.returncode != 0:
                print(f"Failed to execute: {cmd}")
                return False
        
        print("✓ Base environment created successfully")
        return True
    
    print("✗ Failed to create base environment")
    return False

def create_llm_environment():
    """Create environment for LLM fine-tuning"""
    print("\n=== Creating LLM Fine-tuning Environment ===")
    
    # Remove existing environment if it exists
    run_command("conda env remove -n reclm_llm -y", check=False)
    
    # Create new environment
    if os.path.exists("environment_llm.yml"):
        result = run_command("conda env create -f environment_llm.yml")
        if result and result.returncode == 0:
            print("✓ LLM environment created successfully")
            return True
    else:
        print("environment_llm.yml not found, creating manually...")
        commands = [
            "conda create -n reclm_llm python=3.9 -y",
            "conda activate reclm_llm && conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y",
            "conda activate reclm_llm && pip install wandb==0.16.2 transformers==4.36.2 trl==0.7.9 peft==0.7.2",
            "conda activate reclm_llm && pip install datasets accelerate bitsandbytes"
        ]
        
        for cmd in commands:
            result = run_command(cmd)
            if not result or result.returncode != 0:
                print(f"Failed to execute: {cmd}")
                return False
        
        print("✓ LLM environment created successfully")
        return True
    
    print("✗ Failed to create LLM environment")
    return False

def main():
    print("RecLM Environment Setup")
    print("=" * 50)
    
    if not check_conda():
        print("Error: Conda is not available. Please install Anaconda or Miniconda.")
        sys.exit(1)
    
    print("✓ Conda is available")
    
    # Create environments
    base_success = create_base_environment()
    llm_success = create_llm_environment()
    
    print("\n" + "=" * 50)
    print("Setup Summary:")
    print(f"Base models environment: {'✓ Success' if base_success else '✗ Failed'}")
    print(f"LLM fine-tuning environment: {'✓ Success' if llm_success else '✗ Failed'}")
    
    if base_success and llm_success:
        print("\n🎉 All environments created successfully!")
        print("\nUsage:")
        print("For base models: conda activate reclm_base")
        print("For LLM fine-tuning: conda activate reclm_llm")
        print("\nNext steps:")
        print("1. Test base models: conda activate reclm_base && python base_models/BiasMF/Main.py --data netflix")
        print("2. Test LLM setup: conda activate reclm_llm && python llm/lora/sft_base.py")
    else:
        print("\n❌ Some environments failed to create. Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
