#!/bin/bash

echo "Setting up RecLM environments..."

# Check if conda is installed
if ! command -v conda &> /dev/null; then
    echo "Conda is not installed. Please install <PERSON><PERSON><PERSON> or <PERSON>con<PERSON> first."
    exit 1
fi

# Create base models environment
echo "Creating base models environment (reclm_base)..."
conda env create -f environment_base.yml

# Create LLM fine-tuning environment  
echo "Creating LLM fine-tuning environment (reclm_llm)..."
conda env create -f environment_llm.yml

echo "Environment setup complete!"
echo ""
echo "To use the environments:"
echo "For base models: conda activate reclm_base"
echo "For LLM fine-tuning: conda activate reclm_llm"
echo ""
echo "Next steps:"
echo "1. Test base models with: conda activate reclm_base && python base_models/BiasMF/Main.py --data netflix"
echo "2. Test LLM setup with: conda activate reclm_llm && python llm/lora/sft_base.py"
