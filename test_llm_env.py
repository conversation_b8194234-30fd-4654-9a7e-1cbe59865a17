#!/usr/bin/env python3
"""
Test script to verify LLM environment setup
"""

import os
os.environ["WANDB_DISABLED"] = "true"

print("Testing LLM environment...")

try:
    import torch
    print(f"✓ PyTorch: {torch.__version__}")
except ImportError as e:
    print(f"✗ PyTorch import failed: {e}")
    exit(1)

try:
    import transformers
    print(f"✓ Transformers: {transformers.__version__}")
except ImportError as e:
    print(f"✗ Transformers import failed: {e}")
    exit(1)

try:
    import peft
    print(f"✓ PEFT: {peft.__version__}")
except ImportError as e:
    print(f"✗ PEFT import failed: {e}")
    exit(1)

try:
    import trl
    print(f"✓ TRL: {trl.__version__}")
except ImportError as e:
    print(f"✗ TRL import failed: {e}")
    exit(1)

try:
    import wandb
    print(f"✓ WandB: {wandb.__version__}")
except ImportError as e:
    print(f"✗ WandB import failed: {e}")
    exit(1)

try:
    from datasets import load_from_disk
    print("✓ Datasets library working")
except ImportError as e:
    print(f"✗ Datasets import failed: {e}")
    exit(1)

# Test loading the SFT data
try:
    dataset = load_from_disk("./sft_data/netflix/netflix_hf")
    print(f"✓ SFT dataset loaded successfully: {len(dataset)} samples")
    
    # Show a sample
    sample = dataset[0]
    print(f"✓ Sample data keys: {list(sample.keys())}")
    
except Exception as e:
    print(f"✗ Failed to load SFT dataset: {e}")
    exit(1)

print("\n🎉 LLM environment test completed successfully!")
print("Ready for fine-tuning!")
