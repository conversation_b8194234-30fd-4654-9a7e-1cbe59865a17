#!/usr/bin/env python3
"""
Download pre-trained RecLM models from HuggingFace
"""

import os
from huggingface_hub import snapshot_download
import shutil

def download_reclm_models():
    """Download RecLM models from HuggingFace repository"""
    
    print("Downloading RecLM pre-trained models from HuggingFace...")
    
    # Create ft_models directory if it doesn't exist
    ft_models_dir = "llm/ft_models"
    os.makedirs(ft_models_dir, exist_ok=True)
    
    try:
        # Download the entire repository
        repo_path = snapshot_download(
            repo_id="hkuds/RecLM_model",
            local_dir=ft_models_dir,
            local_dir_use_symlinks=False
        )
        
        print(f"✓ Models downloaded successfully to: {ft_models_dir}")
        
        # List downloaded contents
        print("\nDownloaded model structure:")
        for root, dirs, files in os.walk(ft_models_dir):
            level = root.replace(ft_models_dir, '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files[:5]:  # Show first 5 files only
                print(f"{subindent}{file}")
            if len(files) > 5:
                print(f"{subindent}... and {len(files) - 5} more files")
        
        return True
        
    except Exception as e:
        print(f"✗ Failed to download models: {e}")
        return False

def verify_model_structure():
    """Verify that the expected model files are present"""
    
    print("\nVerifying model structure...")
    
    expected_paths = [
        "llm/ft_models/netflix",
        "llm/ft_models/mind", 
    ]
    
    all_good = True
    for path in expected_paths:
        if os.path.exists(path):
            print(f"✓ Found: {path}")
        else:
            print(f"✗ Missing: {path}")
            all_good = False
    
    return all_good

if __name__ == "__main__":
    print("RecLM Model Download Script")
    print("=" * 50)
    
    success = download_reclm_models()
    
    if success:
        verify_model_structure()
        print("\n🎉 Model download completed!")
        print("\nNext steps:")
        print("1. Run profile generation: python llm/lora/inference_base.py")
        print("2. Test base models with generated profiles")
    else:
        print("\n❌ Model download failed. Please check your internet connection and try again.")
